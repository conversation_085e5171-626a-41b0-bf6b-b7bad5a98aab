apiVersion: apps/v1
kind: Deployment
metadata:
  name: website-ssr-service-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v0.7.38 # {"lp-deploy-tag-updater:version": "website-ssr-service"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: website-ssr-service-sa-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v0.7.38 # {"lp-deploy-tag-updater:version": "website-ssr-service"}
    spec:
      containers:
        - name: website-ssr-service
          resources:
            limits:
              cpu: "1500m"
              memory: "1Gi"
            requests:
              cpu: "1"
              memory: "1Gi"
