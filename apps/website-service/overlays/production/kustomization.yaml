apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - render-service-hpa.yaml
patches:
  - path: deploy.yaml
  - path: ingress.yaml
  - path: sa.yaml
  - path: hpa.yaml
  - path: load-website-migration-job.yaml
  - path: pdb.yaml
  - path: render-service-deployment.yaml
  - path: render-service-pdb.yaml
  - path: render-service-sa.yaml
  - path: purge-service-deployment.yaml
  - path: purge-service-sa.yaml
  - path: graphql-publish.yaml
  - patch: |-
      - op: replace
        path: /metadata/name
        value: website-service-graphql-publish-
    target:
      kind: Job
      name: app-cronjob-graphql
      version: v1
      group: batch
images:
  - name: luxurypresence/load-website-migration
    newName: luxurypresence/load-website-migration
    newTag: v1.42.6 # {"lp-deploy-tag-updater:version": "website-service"}
  - name: luxurypresence/app
    newName: luxurypresence/website-service
    newTag: v1.42.6 # {"lp-deploy-tag-updater:version": "website-service"}

transformers:
  - version-suffixer.yaml