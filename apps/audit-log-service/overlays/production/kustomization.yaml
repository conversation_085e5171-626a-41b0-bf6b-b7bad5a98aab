apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - path: graphql-deploy.yaml
  # - path: kafka-deploy.yaml
  - path: sa.yaml
  - path: graphql-publish.yaml
  # Remove kafka deployment until external setup is complete
  - patch: |-
      $patch: delete
      apiVersion: apps/v1
      kind: Deployment
      metadata:
        name: audit-log-kafka-service-deployment
  - patch: |-
      - op: replace
        path: /metadata/name
        value: audit-log-service-graphql-publish-
    target:
      kind: Job
      name: app-cronjob-graphql
      version: v1
      group: batch
images:
  - name: luxurypresence/load-audit-log-service-migration
    newName: luxurypresence/load-audit-log-service-migration
    newTag: v2.9.0 # {"lp-deploy-tag-updater:version": "audit-log-service"}
  - name: luxurypresence/app
    newName: luxurypresence/audit-log-service
    newTag: v2.6.0 # {"lp-deploy-tag-updater:version": "audit-log-service"}

transformers:
  - version-suffixer.yaml
