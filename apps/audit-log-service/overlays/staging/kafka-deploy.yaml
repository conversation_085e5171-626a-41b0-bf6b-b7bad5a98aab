apiVersion: apps/v1
kind: Deployment
metadata:
  name: audit-log-kafka-service-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v2.9.0 # {"lp-deploy-tag-updater:version": "audit-log-service"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v2.9.0 # {"lp-deploy-tag-updater:version": "audit-log-service"}
    spec:
      initContainers:
        - name: grab-config
        - name: render-config
          env:
            - name: VAULT_LOG_LEVEL
              value: ERROR
      containers:
        - name: audit-log-kafka-service
          env:
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:secret/data/staging/audit-log-service#DB_USERNAME}:${vault:secret/data/staging/audit-log-service#DB_PASSWORD}@${vault:secret/data/staging/audit-log-service#DB_HOST}:5432/${vault:secret/data/staging/audit-log-service#DB}'
            - name: SERVICE_MODE
              value: 'connector'
            - name: KAFKA_API_KEY
              value: '${vault:secret/data/staging/audit-log-service#KAFKA_API_KEY}'
            - name: KAFKA_API_SECRET
              value: '${vault:secret/data/staging/audit-log-service#KAFKA_API_SECRET}'
            - name: KAFKA_SCHEMA_API_KEY
              value: '${vault:secret/data/staging/audit-log-service#KAFKA_SCHEMA_API_KEY}'
            - name: KAFKA_SCHEMA_API_SECRET
              value: '${vault:secret/data/staging/audit-log-service#KAFKA_SCHEMA_API_SECRET}'
            - name: KAFKA_BROKER_URL
              value: '${vault:secret/data/staging/audit-log-service#KAFKA_BROKER_URL}'
            - name: KAFKA_SCHEMA_URL
              value: '${vault:secret/data/staging/audit-log-service#KAFKA_SCHEMA_URL}'
            - name: KAFKA_CDC_TOPICS
              value: '${vault:secret/data/staging/audit-log-service#KAFKA_CDC_TOPICS}'
            - name: KAFKA_CONSUMER_GROUP
              value: '${vault:secret/data/staging/audit-log-service#KAFKA_CONSUMER_GROUP}'
            - name: VAULT_LOG_LEVEL
              value: ERROR
            - name: DD_LOGS_INJECTION
              value: "true"
          resources:
            limits:
              cpu: "500m"
              memory: "512Mi"
            requests:
              cpu: "100m"
              memory: "256Mi"
