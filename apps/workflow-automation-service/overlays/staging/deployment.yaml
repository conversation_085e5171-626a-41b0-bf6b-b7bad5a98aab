apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-automation-service-deployment
  labels:
    app: WorkflowAutomationService
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v0.2.0 # {"lp-deploy-tag-updater:version": "workflow-automation-service"}
spec:
  template:
    metadata:
      labels:
        app: WorkflowAutomationService
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v0.2.0 # {"lp-deploy-tag-updater:version": "workflow-automation-service"}
