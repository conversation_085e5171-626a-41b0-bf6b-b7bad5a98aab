apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - e2e-job.yaml
  - e2e-sanity-job.yaml
  - sa-e2e.yaml
patches:
  - target:
      group: batch
      kind: Job
      name: website-api-gateway-static-cp-job
      version: v1
    patch: |-
      - op: replace
        path: /metadata/name
        value: website-api-gateway-static-cp-job-staging-
  - path: ingress.yaml
  - path: deploy.yaml
  - path: deploy-consumer.yaml
  - path: sa.yaml
  - path: static-cp-job.yaml
  - path: hpa.yaml
  - path: hpa-consumer.yaml
  - path: static-cp-job-old.yaml
images:
  - name: luxurypresence/home-search-tests-ete
    newTag: v7.8.0 # {"lp-deploy-tag-updater:version": "home-search-tests-ete"}
  - name: luxurypresence/app
    newName: luxurypresence/website-api-gateway
    newTag: v8.37.1 # {"lp-deploy-tag-updater:version": "website-api-gateway"}
transformers:
  - version-suffixer.yaml
