apiVersion: batch/v1
kind: Job
metadata:
  name: load-audit-log-service-migration
  annotations:
    argocd.argoproj.io/hook: PreSync
spec:
  template:
    metadata:
      annotations:
        karpenter.sh/do-not-disrupt: "true"
    spec:
      serviceAccountName: audit-log-service
      containers:
        - name: load-audit-log-service-migration
          image: luxurypresence/load-audit-log-service-migration
          imagePullPolicy: Always
          env:
            - name: PG_CONNECTION_STRING
              value: "to_be_set"
          resources:
            limits:
              cpu: "1"
              memory: "1Gi"
            requests:
              cpu: "0.5"
              memory: "500Mi"
      restartPolicy: OnFailure
      imagePullSecrets:
        - name: image-pull-secret
