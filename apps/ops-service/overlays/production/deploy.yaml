apiVersion: apps/v1
kind: Deployment
metadata:
  name: ops-service-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v0.23.0 # {"lp-deploy-tag-updater:version": "ops-service"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: ops-service-sa-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v0.23.0 # {"lp-deploy-tag-updater:version": "ops-service"}
    spec:
      containers:
        - name: ops-service
          env:
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-production-ops-service-long-life-v3#username}:${vault:database/creds/postgres-production-ops-service-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}'
              valueFrom:
                $patch: delete
            - name: PG_READER_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-production-ops-service-long-life-v3#username}:${vault:database/creds/postgres-production-ops-service-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#READER_HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}'
            - name: JOB_TYPE
              value: SERVICE_DEPLOYMENT
          resources:
            limits:
              cpu: "1"
              memory: "615Mi"
            requests:
              cpu: "500m"
              memory: "615Mi"
