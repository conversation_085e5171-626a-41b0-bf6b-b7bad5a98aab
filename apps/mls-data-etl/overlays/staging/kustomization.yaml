apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - sa-dags-pods.yaml
  - external_secrets.yaml
transformers:
  - version-suffixer.yaml
images:
  - name: luxurypresence/data-etl-dags
    newTag: main-14867-8afd2fc # {"$imagepolicy": "flux-system:mls-data-etl-flux-image-policy-staging-new:tag"}
patches:
  - path: job.yaml
  - path: sa.yaml
  - path: cronjob.yaml
  - target:
      group: batch
      version: v1
      kind: Job
      name: mls-data-etl-dags-job
    patch: |-
      - op: replace
        path: /metadata/name
        value: mls-data-etl-dags-job-staging-
  - target:
      group: batch
      kind: CronJob
      name: mwaa-pod-terminator
      version: v1
    patch: |-
      - op: replace
        path: /metadata/namespace
        value: mwaa-staging
  - target:
      group: rbac.authorization.k8s.io
      version: v1
    patch: |-
      - op: replace
        path: /metadata/namespace
        value: mwaa-staging
  - target:
      version: v1
      kind: ServiceAccount
      name: mwaa-pod-terminator
    patch: |-
      - op: replace
        path: /metadata/namespace
        value: mwaa-staging
